2025-08-13 14:20:41,115 - DEBUG - Starting GCP bill processing for AWS migration scoping.
2025-08-13 14:20:41,115 - DEBUG - GCP CSV file path: /Users/<USER>/Documents/PS-3/untitled folder/gcp_bill.csv
2025-08-13 14:20:41,115 - DEBUG - Template Excel path: /Users/<USER>/Documents/PS-3/untitled folder/Azure-to-AWS-migration-scoping-with-Azure-Bills  (2).xlsx
2025-08-13 14:20:41,115 - DEBUG - Output path: /Users/<USER>/Documents/PS-3/untitled folder/GCP-to-AWS-migration-scoping-with-GCP-Bill-output.xlsx
2025-08-13 14:20:41,116 - INFO - Reading data from GCP bill CSV: '/Users/<USER>/Documents/PS-3/untitled folder/gcp_bill.csv'
2025-08-13 14:20:41,120 - DEBUG - CSV columns found: ['Service description', 'Service ID', 'Cost ($)', 'Savings programs ($)', 'Other savings ($)', 'Unrounded subtotal ($)', 'Subtotal ($)', 'Percent change in subtotal compared to previous period']
2025-08-13 14:20:41,124 - DEBUG - Extracted: Service='Cloud SQL', Cost='2544.81'
2025-08-13 14:20:41,124 - DEBUG - Extracted: Service='Vertex AI', Cost='81.35'
2025-08-13 14:20:41,124 - DEBUG - Extracted: Service='Notebooks', Cost='17.35'
2025-08-13 14:20:41,124 - DEBUG - Extracted: Service='Compute Engine', Cost='11.39'
2025-08-13 14:20:41,124 - DEBUG - Extracted: Service='Networking', Cost='8.32'
2025-08-13 14:20:41,124 - DEBUG - Extracted: Service='Cloud Monitoring', Cost='2.62'
2025-08-13 14:20:41,124 - DEBUG - Extracted: Service='Gemini API', Cost='1.27'
2025-08-13 14:20:41,124 - DEBUG - Extracted: Service='VM Manager', Cost='0.77'
2025-08-13 14:20:41,124 - DEBUG - Extracted: Service='Cloud Run', Cost='0.7'
2025-08-13 14:20:41,124 - DEBUG - Extracted: Service='Cloud DNS', Cost='0.18'
2025-08-13 14:20:41,124 - DEBUG - Extracted: Service='Cloud Dialogflow API', Cost='0.01'
2025-08-13 14:20:41,124 - DEBUG - Extracted: Service='Cloud Storage', Cost='0.0'
2025-08-13 14:20:41,124 - DEBUG - Extracted: Service='Artifact Registry', Cost='0.0'
2025-08-13 14:20:41,124 - DEBUG - Extracted: Service='BigQuery', Cost='0.0'
2025-08-13 14:20:41,124 - DEBUG - Extracted: Service='Eventarc', Cost='0.0'
2025-08-13 14:20:41,124 - DEBUG - Extracted: Service='Secret Manager', Cost='0.0'
2025-08-13 14:20:41,124 - INFO - Extracted 16 data rows from the GCP CSV bill.
2025-08-13 14:20:41,124 - INFO - Loading destination template to create a copy: '/Users/<USER>/Documents/PS-3/untitled folder/Azure-to-AWS-migration-scoping-with-Azure-Bills  (2).xlsx'
2025-08-13 14:20:41,179 - DEBUG - Found destination sheet: 'Consumption Mapping'
2025-08-13 14:20:41,179 - DEBUG - Sheet dimensions: 286 rows x 27 columns
2025-08-13 14:20:41,180 - DEBUG - Destination headers: ['AZURE Service', 'Searce In Scope', 'Treatment', 'Consumption', 'Volume', 'AWS Equivalent', 'Migration Effort', 'Responsible Team', 'Service Category', 'Comments/Approach', 'Cloud Team Tasks', 'App Team Tasks', 'Data Team Tasks', None, None, None, None, None, None, None, None, None, None, None, None, None, None]
2025-08-13 14:20:41,180 - DEBUG - Found service column 'AZURE Service' at index: 1
2025-08-13 14:20:41,180 - DEBUG - Destination service column index: 1
2025-08-13 14:20:41,180 - DEBUG - Destination consumption column index: 4
2025-08-13 14:20:41,180 - INFO - Clearing old data and writing 16 new rows to the copy.
2025-08-13 14:20:41,181 - DEBUG - Wrote to row 2: {'service': 'Cloud SQL', 'consumption': 2544.81}
2025-08-13 14:20:41,181 - DEBUG - Wrote to row 3: {'service': 'Vertex AI', 'consumption': 81.35}
2025-08-13 14:20:41,181 - DEBUG - Wrote to row 4: {'service': 'Notebooks', 'consumption': 17.35}
2025-08-13 14:20:41,181 - DEBUG - Wrote to row 5: {'service': 'Compute Engine', 'consumption': 11.39}
2025-08-13 14:20:41,181 - DEBUG - Wrote to row 6: {'service': 'Networking', 'consumption': 8.32}
2025-08-13 14:20:41,181 - DEBUG - Wrote to row 7: {'service': 'Cloud Monitoring', 'consumption': 2.62}
2025-08-13 14:20:41,181 - DEBUG - Wrote to row 8: {'service': 'Gemini API', 'consumption': 1.27}
2025-08-13 14:20:41,181 - DEBUG - Wrote to row 9: {'service': 'VM Manager', 'consumption': 0.77}
2025-08-13 14:20:41,181 - DEBUG - Wrote to row 10: {'service': 'Cloud Run', 'consumption': 0.7}
2025-08-13 14:20:41,181 - DEBUG - Wrote to row 11: {'service': 'Cloud DNS', 'consumption': 0.18}
2025-08-13 14:20:41,181 - DEBUG - Wrote to row 12: {'service': 'Cloud Dialogflow API', 'consumption': 0.01}
2025-08-13 14:20:41,181 - DEBUG - Wrote to row 13: {'service': 'Cloud Storage', 'consumption': 0.0}
2025-08-13 14:20:41,181 - DEBUG - Wrote to row 14: {'service': 'Artifact Registry', 'consumption': 0.0}
2025-08-13 14:20:41,181 - DEBUG - Wrote to row 15: {'service': 'BigQuery', 'consumption': 0.0}
2025-08-13 14:20:41,181 - DEBUG - Wrote to row 16: {'service': 'Eventarc', 'consumption': 0.0}
2025-08-13 14:20:41,181 - DEBUG - Wrote to row 17: {'service': 'Secret Manager', 'consumption': 0.0}
2025-08-13 14:20:41,253 - INFO - Successfully updated 'Consumption Mapping' and saved to '/Users/<USER>/Documents/PS-3/untitled folder/GCP-to-AWS-migration-scoping-with-GCP-Bill-output.xlsx'.
2025-08-13 14:20:41,253 - DEBUG - GCP bill processing finished successfully.
