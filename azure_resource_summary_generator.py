#!/usr/bin/env python3
"""
Azure Resource Inventory Summary Generator
This script processes Azure Resource Inventory Excel reports and generates a comprehensive summary.
"""

import pandas as pd
import openpyxl
from openpyxl.styles import PatternFill, Font, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows
import os
import sys
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AzureResourceSummaryGenerator:
    def __init__(self, input_file):
        self.input_file = input_file
        self.workbook = None
        self.all_sheets = {}
        self.summary_data = {}
        
    def load_excel_file(self):
        """Load the Excel file and read all sheets"""
        try:
            logger.info(f"Loading Excel file: {self.input_file}")
            self.all_sheets = pd.read_excel(self.input_file, sheet_name=None)
            logger.info(f"Successfully loaded {len(self.all_sheets)} sheets")
            return True
        except Exception as e:
            logger.error(f"Error loading Excel file: {str(e)}")
            return False
    
    def analyze_sheets(self):
        """Analyze all sheets and extract key metrics"""
        logger.info("Analyzing all sheets...")
        
        for sheet_name, df in self.all_sheets.items():
            logger.info(f"Processing sheet: {sheet_name}")
            
            # Skip non-resource sheets
            if sheet_name in ['Advisory', 'Advisor', 'Subscriptions', 'Policy', 'Overview', 'All Summary']:
                continue
                
            # Basic metrics
            total_rows = len(df)
            if total_rows == 0:
                continue
                
            # Get column names
            columns = df.columns.tolist()
            
            # Try to identify subscription/project column
            subscription_col = None
            for col in columns:
                if any(keyword in str(col).lower() for keyword in ['subscription', 'sub', 'project', 'resource group']):
                    subscription_col = col
                    break
            
            # If no subscription column found, use first column
            if subscription_col is None and len(columns) > 0:
                subscription_col = columns[0]
            
            # Count by subscription/project
            if subscription_col and subscription_col in df.columns:
                subscription_counts = df[subscription_col].value_counts()
            else:
                subscription_counts = pd.Series()
            
            # Store summary data
            self.summary_data[sheet_name] = {
                'total_resources': total_rows,
                'columns': columns,
                'subscription_col': subscription_col,
                'subscription_counts': subscription_counts,
                'unique_subscriptions': len(subscription_counts),
                'top_subscriptions': subscription_counts.head(5).to_dict()
            }
    
    def generate_summary_sheet(self):
        """Generate the summary sheet with key metrics"""
        logger.info("Generating summary sheet...")
        
        # Create summary dataframe
        summary_rows = []
        
        for sheet_name, data in self.summary_data.items():
            if data['subscription_counts'].empty:
                continue
                
            for subscription, count in data['subscription_counts'].items():
                summary_rows.append({
                    'Sheet Name': sheet_name,
                    'Subscription/Resource Group': subscription,
                    'Resource Count': count
                })
        
        if not summary_rows:
            logger.warning("No data found for summary")
            return pd.DataFrame()
        
        summary_df = pd.DataFrame(summary_rows)
        
        # Create pivot table for better visualization
        pivot_df = summary_df.pivot_table(
            index='Subscription/Resource Group',
            columns='Sheet Name',
            values='Resource Count',
            fill_value=0,
            aggfunc='sum'
        )
        
        # Add total column
        pivot_df['Total Resources'] = pivot_df.sum(axis=1)
        
        # Sort by total resources
        pivot_df = pivot_df.sort_values('Total Resources', ascending=False)
        
        return pivot_df
    
    def generate_overview_summary(self):
        """Generate high-level overview summary"""
        logger.info("Generating overview summary...")
        
        overview_data = []
        
        for sheet_name, data in self.summary_data.items():
            overview_data.append({
                'Resource Type': sheet_name,
                'Total Resources': data['total_resources'],
                'Unique Subscriptions': data['unique_subscriptions'],
                'Main Subscription': list(data['top_subscriptions'].keys())[0] if data['top_subscriptions'] else 'N/A',
                'Main Subscription Count': list(data['top_subscriptions'].values())[0] if data['top_subscriptions'] else 0
            })
        
        overview_df = pd.DataFrame(overview_data)
        overview_df = overview_df.sort_values('Total Resources', ascending=False)
        
        return overview_df
    
    def create_summary_report(self, output_file=None):
        """Create the complete summary report"""
        if not self.load_excel_file():
            return False
        
        self.analyze_sheets()
        
        # Generate summaries
        detailed_summary = self.generate_summary_sheet()
        overview_summary = self.generate_overview_summary()
        
        # Determine output file
        if output_file is None:
            base_name = os.path.splitext(self.input_file)[0]
            output_file = f"{base_name}_Summary_Report.xlsx"
        
        try:
            # Create Excel writer
            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                # Write overview summary
                overview_summary.to_excel(writer, sheet_name='Overview Summary', index=False)
                
                # Write detailed summary
                if not detailed_summary.empty:
                    detailed_summary.to_excel(writer, sheet_name='Detailed Summary')
                
                # Write raw data sheets (optional)
                for sheet_name, df in self.all_sheets.items():
                    if sheet_name not in ['All Summary']:
                        safe_sheet_name = sheet_name[:31]  # Excel sheet name limit
                        df.to_excel(writer, sheet_name=safe_sheet_name, index=False)
            
            # Format the summary file
            self.format_summary_file(output_file)
            
            logger.info(f"Summary report created successfully: {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"Error creating summary report: {str(e)}")
            return False
    
    def format_summary_file(self, file_path):
        """Apply formatting to the summary file"""
        try:
            workbook = openpyxl.load_workbook(file_path)
            
            # Format Overview Summary sheet
            if 'Overview Summary' in workbook.sheetnames:
                ws = workbook['Overview Summary']
                
                # Header formatting
                header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                header_font = Font(color="FFFFFF", bold=True)
                
                for cell in ws[1]:
                    cell.fill = header_fill
                    cell.font = header_font
                    cell.alignment = Alignment(horizontal="center")
                
                # Auto-adjust column widths
                for column in ws.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    ws.column_dimensions[column_letter].width = adjusted_width
            
            # Format Detailed Summary sheet
            if 'Detailed Summary' in workbook.sheetnames:
                ws = workbook['Detailed Summary']
                
                # Header formatting
                for cell in ws[1]:
                    cell.fill = header_fill
                    cell.font = header_font
                    cell.alignment = Alignment(horizontal="center")
                
                # Auto-adjust column widths
                for column in ws.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    ws.column_dimensions[column_letter].width = adjusted_width
            
            workbook.save(file_path)
            logger.info("Formatting applied successfully")
            
        except Exception as e:
            logger.error(f"Error formatting file: {str(e)}")

def main():
    """Main function to run the summary generator"""
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
    else:
        input_file = "lbc/AzureResourceInventory_Report_2025-08-13_19_03.xlsx"
    
    if not os.path.exists(input_file):
        logger.error(f"Input file not found: {input_file}")
        return
    
    generator = AzureResourceSummaryGenerator(input_file)
    success = generator.create_summary_report()
    
    if success:
        logger.info("Summary generation completed successfully!")
    else:
        logger.error("Summary generation failed!")

if __name__ == "__main__":
    main()
