#!/usr/bin/env python3
"""
Azure Resource Inventory Summary Generator - Simple version matching existing logic
"""

import pandas as pd
import os
import sys
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_subscription_counts_from_sheet(sheet_df):
    """Extract subscription counts from a sheet - same logic as existing code"""
    # Try to find subscription column (similar to project counts in existing code)
    columns = sheet_df.columns.tolist()
    
    # Look for subscription-related column
    subscription_col = None
    for col in columns:
        col_name = str(col).lower()
        if any(keyword in col_name for keyword in ['subscription', 'sub', 'subscription name', 'subscription id']):
            subscription_col = col
            break
    
    # If no subscription column found, use first column
    if subscription_col is None and len(columns) > 0:
        subscription_col = columns[0]
    
    # Count by subscription
    if subscription_col and subscription_col in sheet_df.columns:
        subscription_counts = sheet_df[subscription_col].value_counts()
    else:
        subscription_counts = pd.Series()
    
    return subscription_counts

def create_aa_summary(file_path):
    """Create AA Summary sheet following the exact logic from existing code"""
    logger.info("Creating AA Summary...")
    
    # Read all sheets from the Excel file
    all_sheets = pd.read_excel(file_path, sheet_name=None)
    logger.info(f"Loaded {len(all_sheets)} sheets")
    
    subscription_counts_dict = {}
    
    # Loop through each sheet and extract subscription counts
    for sheet_name, sheet_df in all_sheets.items():
        # Skip non-resource sheets (same logic as existing code)
        if sheet_name in ['Advisory', 'Advisor', 'Subscriptions', 'Policy', 'Overview']:
            continue
        
        logger.info(f"Processing sheet: {sheet_name}")
        sheet_counts = get_subscription_counts_from_sheet(sheet_df)
        subscription_counts_dict[sheet_name] = sheet_counts
    
    # Create summary dataframe (same logic as existing code)
    summary_df = pd.DataFrame(subscription_counts_dict).fillna(0).astype(int)
    
    # Add row-wise total (same as existing code)
    summary_df.loc['Total'] = summary_df.sum(axis=0)
    
    # Set the index label for Azure (same as existing code)
    index_label = 'Subscription'
    
    # Write the summary to the original file
    output_file = file_path
    
    # Write the summary sheet first
    with pd.ExcelWriter('./summary_temp.xlsx', engine='openpyxl') as writer:
        summary_df.to_excel(writer, sheet_name='AA Summary', index_label=index_label)
    
    # Append all other sheets to the file (same as existing code)
    with pd.ExcelWriter('./summary_temp.xlsx', engine='openpyxl', mode='a') as writer:
        for sheet_name, sheet_df in all_sheets.items():
            if sheet_name != 'All Summary':  # Skip the summary sheet
                sheet_df.to_excel(writer, sheet_name=sheet_name, index=False)
    
    # Replace original file (same as existing code)
    os.replace('summary_temp.xlsx', output_file)
    
    logger.info("AA Summary created successfully!")
    return True

def main():
    """Main function"""
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
    else:
        input_file = "lbc/AzureResourceInventory_Report_2025-08-13_19_03.xlsx"
    
    if not os.path.exists(input_file):
        logger.error(f"Input file not found: {input_file}")
        return
    
    success = create_aa_summary(input_file)
    
    if success:
        logger.info("Summary generation completed successfully!")
    else:
        logger.error("Summary generation failed!")

if __name__ == "__main__":
    main()
