import openpyxl
import pandas as pd
import os
import logging

# --- Configuration Constants for GCP Bill Processing ---
# Name of the sheet to update in the destination workbook
TARGET_SHEET_GCP = "Consumption Mapping"

# Column names in the source GCP bill CSV file
SOURCE_SERVICE_COLUMN = 'Service description'
SOURCE_COST_COLUMN = 'Cost ($)'

# Column names in the destination/template Excel file
# Using a list to check for possible variations like 'GCP Services', 'AZURE Service', etc.
DESTINATION_SERVICE_COLUMNS = ['GCP Services', 'AZURE Service', 'Azure Services']
DESTINATION_CONSUMPTION_COLUMN = 'Consumption'

# The row number where the headers are located in the source GCP CSV (1-based)
HEADER_ROW_GCP = 1
# The row number where the data starts in the destination sheet (e.g., after the header)
START_ROW_GCP = 2


def process_gcp_bill_to_aws(gcp_csv_path, template_excel_path, rootpath):
    """
    Processes a GCP bill CSV file, extracts service and cost data,
    and populates it into a copy of a standardized AWS migration scoping template.
    All operations are logged to app.log.

    Args:
        gcp_csv_path (str): Path to the GCP bill CSV file.
        template_excel_path (str): Path to the template Excel file.
        rootpath (str): The root directory path of the application.

    Returns:
        tuple: A tuple containing (output_path, error_message, success_message, status_code).
               - output_path (str): Path to the generated Excel file on success.
               - error_message (str): A description of the error if one occurred.
               - success_message (str): A confirmation message on success.
               - status_code (int): 0 for success, 1 for failure.
    """
    # --- Setup Logging ---
    log_file_path = os.path.join(rootpath, 'app.log')
    # Set logging level to DEBUG to capture all levels of logs
    logging.basicConfig(
        filename=log_file_path,
        level=logging.DEBUG,
        format='%(asctime)s - %(levelname)s - %(message)s',
        force=True  # Use force=True to allow re-configuration if run multiple times
    )

    try:
        logging.debug("Starting GCP bill processing for AWS migration scoping.")
        
        # --- 1. Define File Paths ---
        output_path = f"{rootpath}/GCP-to-AWS-migration-scoping-with-GCP-Bill-output.xlsx"
        logging.debug(f"GCP CSV file path: {gcp_csv_path}")
        logging.debug(f"Template Excel path: {template_excel_path}")
        logging.debug(f"Output path: {output_path}")

        # --- 2. Read and Extract Data from GCP CSV Bill ---
        logging.info(f"Reading data from GCP bill CSV: '{gcp_csv_path}'")
        
        if not os.path.exists(gcp_csv_path):
            error_msg = f"GCP CSV file not found at '{gcp_csv_path}'."
            logging.error(error_msg)
            raise FileNotFoundError(error_msg)
        
        # Read CSV using pandas for easier data manipulation
        gcp_df = pd.read_csv(gcp_csv_path)
        logging.debug(f"CSV columns found: {gcp_df.columns.tolist()}")
        
        # Check if required columns exist
        if SOURCE_SERVICE_COLUMN not in gcp_df.columns:
            error_msg = f"Required column '{SOURCE_SERVICE_COLUMN}' not found in GCP CSV."
            logging.error(error_msg)
            raise ValueError(error_msg)
            
        if SOURCE_COST_COLUMN not in gcp_df.columns:
            error_msg = f"Required column '{SOURCE_COST_COLUMN}' not found in GCP CSV."
            logging.error(error_msg)
            raise ValueError(error_msg)

        # Extract and sort data by cost (descending order)
        gcp_services = gcp_df[[SOURCE_SERVICE_COLUMN, SOURCE_COST_COLUMN]].copy()
        gcp_services = gcp_services.sort_values(SOURCE_COST_COLUMN, ascending=False)
        
        # Convert to list of dictionaries for easier processing
        data_to_paste = []
        for _, row in gcp_services.iterrows():
            service_name = row[SOURCE_SERVICE_COLUMN]
            cost = row[SOURCE_COST_COLUMN]
            
            # Skip empty or invalid entries
            if pd.isna(service_name) or service_name == '':
                continue
                
            data_to_paste.append({'service': service_name, 'consumption': cost})
            logging.debug(f"Extracted: Service='{service_name}', Cost='{cost}'")

        logging.info(f"Extracted {len(data_to_paste)} data rows from the GCP CSV bill.")

        # --- 3. Load the Template and Prepare the Copy ---
        if not os.path.exists(template_excel_path):
            error_msg = f"Destination template file not found at '{template_excel_path}'."
            logging.error(error_msg)
            raise FileNotFoundError(error_msg)

        logging.info(f"Loading destination template to create a copy: '{template_excel_path}'")
        dest_workbook = openpyxl.load_workbook(template_excel_path)
        
        if TARGET_SHEET_GCP not in dest_workbook.sheetnames:
            error_msg = f"Target sheet '{TARGET_SHEET_GCP}' not found in the template workbook."
            logging.error(error_msg)
            logging.debug(f"Available sheets: {dest_workbook.sheetnames}")
            raise ValueError(error_msg)
        
        dest_sheet = dest_workbook[TARGET_SHEET_GCP]
        logging.debug(f"Found destination sheet: '{dest_sheet.title}'")
        logging.debug(f"Sheet dimensions: {dest_sheet.max_row} rows x {dest_sheet.max_column} columns")
        
        # Find destination columns
        dest_header = [cell.value for cell in dest_sheet[1]]
        logging.debug(f"Destination headers: {dest_header}")
        
        dest_service_col_idx = -1
        for col_name in DESTINATION_SERVICE_COLUMNS:
            if col_name in dest_header:
                dest_service_col_idx = dest_header.index(col_name) + 1
                logging.debug(f"Found service column '{col_name}' at index: {dest_service_col_idx}")
                break
        
        if dest_service_col_idx == -1:
            error_msg = f"Could not find any of the service columns {DESTINATION_SERVICE_COLUMNS} in the template."
            logging.error(error_msg)
            raise ValueError(error_msg)
            
        if DESTINATION_CONSUMPTION_COLUMN not in dest_header:
            error_msg = f"Required column '{DESTINATION_CONSUMPTION_COLUMN}' not found in template."
            logging.error(error_msg)
            raise ValueError(error_msg)
            
        dest_consumption_col_idx = dest_header.index(DESTINATION_CONSUMPTION_COLUMN) + 1
        logging.debug(f"Destination service column index: {dest_service_col_idx}")
        logging.debug(f"Destination consumption column index: {dest_consumption_col_idx}")

        # --- 4. Clear Old Data and Write New GCP Data ---
        logging.info(f"Clearing old data and writing {len(data_to_paste)} new rows to the copy.")
        
        # Clear existing data in the target columns (preserve formatting)
        for row in range(START_ROW_GCP, dest_sheet.max_row + 200):
            dest_sheet.cell(row=row, column=dest_service_col_idx).value = None
            dest_sheet.cell(row=row, column=dest_consumption_col_idx).value = None

        # Write new GCP data
        for index, data_row in enumerate(data_to_paste, start=START_ROW_GCP):
            dest_sheet.cell(row=index, column=dest_service_col_idx).value = data_row['service']
            dest_sheet.cell(row=index, column=dest_consumption_col_idx).value = data_row['consumption']
            logging.debug(f"Wrote to row {index}: {data_row}")

        # --- 5. Save the Updated Copy and Return ---
        dest_workbook.save(output_path)
        dest_workbook.close()
        
        success_msg = f"Successfully updated '{TARGET_SHEET_GCP}' and saved to '{output_path}'."
        logging.info(success_msg)
        logging.debug("GCP bill processing finished successfully.")
        
        return output_path, "", success_msg, 0

    except FileNotFoundError as e:
        logging.error(f"File not found - {e}")
        return "", str(e), str(e), 1
    except ValueError as e:
        logging.error(f"Value error - {e}")
        return "", str(e), str(e), 1
    except Exception as e:
        error_msg = f"An unexpected error occurred: {e}"
        logging.error(error_msg, exc_info=True)  # exc_info=True will log the full traceback
        return "", error_msg, error_msg, 1


def extract_and_update_excel():
    """
    Wrapper function to maintain compatibility with existing code.
    Uses the current directory structure for file paths.
    """
    try:
        # Define file paths based on current directory
        current_dir = os.getcwd()
        gcp_csv_path = os.path.join(current_dir, 'gcp_bill.csv')
        template_excel_path = os.path.join(current_dir, 'Azure-to-AWS-migration-scoping-with-Azure-Bills  (2).xlsx')
        
        # Call the main processing function
        output_path, error_message, success_message, status_code = process_gcp_bill_to_aws(
            gcp_csv_path, template_excel_path, current_dir
        )
        
        if status_code == 0:
            print(f"\n✅ Process completed successfully!")
            print(f"📁 Output file: {output_path}")
            print(f"🔄 {success_message}")
            return output_path
        else:
            print(f"❌ Process failed: {error_message}")
            return None
            
    except Exception as e:
        print(f"❌ Wrapper function failed: {e}")
        return None


if __name__ == "__main__":
    output_file = extract_and_update_excel()
    if output_file:
        print(f"\n✅ Process completed successfully!")
        print(f"📁 Updated file: {output_file}")
        print(f"🔄 The 'Consumption Mapping' sheet has been updated with GCP services and costs")
    else:
        print("❌ Process failed!")
