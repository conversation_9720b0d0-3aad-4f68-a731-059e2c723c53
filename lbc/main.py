def get_project_counts_from_sheet(sheet_df,platform):
    # Assuming the project name is in the first column of each sheet
    if platform == 'aws':
        project_counts = sheet_df.iloc[:, 1].value_counts()
    else:
        project_counts = sheet_df.iloc[:, 0].value_counts()
    return project_counts

def create_summary_sheet(file_path, platform):

    # Read all sheets from the Excel file into a dictionary of DataFrames
    all_sheets = pd.read_excel(file_path, sheet_name=None)
    print("Reached summary sheet generation...")

    project_counts_dict = {}

    # Loop through each sheet and extract project counts
    for sheet_name, sheet_df in all_sheets.items():
        if (platform == 'azure' and (sheet_name in ['Advisory','Advisor','Subscriptions', 'Policy', 'Overview'])) or (platform == 'gcp' and (sheet_name == 'Folder Metadata' or sheet_name == 'Org policies' or sheet_name == 'IAM-policy' or sheet_name == 'Project Metadata')) :
            continue

        sheet_counts = get_project_counts_from_sheet(sheet_df, platform)
        project_counts_dict[sheet_name] = sheet_counts

    # Replace NaN values with 0 (where a project doesn't appear in a sheet)
    summary_df = pd.DataFrame(project_counts_dict).fillna(0).astype(int)

    # Add row-wise total
    summary_df.loc['Total'] = summary_df.sum(axis=0)

    # Set the index label depending on the platform
    if platform == 'azure':
        index_label = 'Subscription'
    elif platform == 'gcp':
        index_label = 'Project'
    elif platform == 'aws':
        index_label = 'Region'
    elif platform == 'oci':
        index_label = 'compartment_name'

    # Write the summary sheet first
    with pd.ExcelWriter('./summary_temp.xlsx', engine='openpyxl') as writer:
        summary_df.to_excel(writer, sheet_name='AA Summary', index_label=index_label)

    # Append all other sheets to the file
    with pd.ExcelWriter('./summary_temp.xlsx', engine='openpyxl', mode='a') as writer:
        for sheet_name, sheet_df in all_sheets.items():

            if sheet_name != 'All Summary':  # Skip the summary sheet
                sheet_df.to_excel(writer, sheet_name=sheet_name, index=False)

    # Replace original file
    os.replace('summary_temp.xlsx', file_path)


def add_target_project_id_azure(file_path):
    try:
        workbook = openpyxl.load_workbook(file_path)
        sheet_names_to_update = ['Virtual Machines', 'Redis Cache', 'SQL DBs']
        sheets_updated = []

        for sheet_name in sheet_names_to_update:
            if sheet_name in workbook.sheetnames:
                sheet = workbook[sheet_name]
                sheet.cell(row=1, column=sheet.max_column + 1, value='TargetProjectID')
                sheet.cell(row=1, column=sheet.max_column + 1, value='TargetName')
                sheets_updated.append(sheet_name)

        if sheets_updated:
            workbook.save(file_path)
            logger.info(f"Added 'targetprojectID' and 'targetprojectName' columns to the following sheets: {', '.join(sheets_updated)}.")
        else:
            logger.warning(f"None of the target sheets ({', '.join(sheet_names_to_update)}) found in the Excel file.")
    except Exception as e:
        logger.exception("Failed to add target project columns: " + str(e))


def add_target_name_id(file_path):
    """
    Adds specified columns to specific sheets in an Excel file and colors
    the new headers green.
    """
    try:
        workbook = openpyxl.load_workbook(file_path)
        # Define the green fill style once for efficiency
        green_fill = PatternFill(start_color='C6E0B4', end_color='C6E0B4', fill_type='solid')
        sheets_updated = []

        for sheet_name, columns in aws_sheet_names_to_update.items():
            if sheet_name in workbook.sheetnames:
                sheet = workbook[sheet_name]
                start_col = sheet.max_column + 1
                
                for i, column_name in enumerate(columns):
                    # Get the cell object while setting its value
                    new_header_cell = sheet.cell(row=1, column=start_col + i, value=column_name)
                    # Apply the green fill to the new header cell
                    new_header_cell.fill = green_fill
                
                sheets_updated.append(sheet_name)

        if sheets_updated:
            workbook.save(file_path)
            logger.info(f"Added and formatted target columns in sheets: {', '.join(sheets_updated)}.")
        else:
            logger.warning(f"None of the target sheets ({', '.join(aws_sheet_names_to_update.keys())}) were found.")
            
    except Exception as e:
        logger.exception("Failed to add and format target columns: " + str(e))

        