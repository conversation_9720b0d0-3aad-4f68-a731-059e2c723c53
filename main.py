import pandas as pd

def extract_and_update_excel():
    """
    Extract Service description and Cost from GCP CSV and update Azure Excel file
    """
    try:
        # Read the GCP CSV file
        print("Reading GCP bill CSV file...")
        gcp_df = pd.read_csv('gcp_bill.csv')

        # Extract Service description and Cost columns
        gcp_services = gcp_df[['Service description', 'Cost ($)']].copy()

        # Sort by cost in descending order
        gcp_services = gcp_services.sort_values('Cost ($)', ascending=False)

        print(f"Extracted {len(gcp_services)} services from GCP bill")
        print("\nGCP Services (sorted by cost):")
        print(gcp_services)

        # Read the Azure Excel file
        print("\nReading Azure Excel file...")
        azure_df = pd.read_excel('Azure-to-AWS-migration-scoping-with-Azure-Bills .xlsx')

        print(f"Original Azure file has {len(azure_df)} rows")

        # Create a new dataframe with GCP data
        # Keep the same number of rows as GCP services, or pad with empty rows if needed
        num_gcp_services = len(gcp_services)
        num_azure_rows = len(azure_df)

        # Create new dataframe with GCP services
        new_df = azure_df.copy()

        # Clear existing data in AZURE Service and Consumption columns
        new_df['AZURE Service'] = ''
        new_df['Consumption'] = ''

        # If we have fewer GCP services than Azure rows, truncate
        if num_gcp_services < num_azure_rows:
            new_df = new_df.head(num_gcp_services)
        # If we have more GCP services, add more rows
        elif num_gcp_services > num_azure_rows:
            # Add empty rows to match GCP services count
            empty_rows = pd.DataFrame(index=range(num_azure_rows, num_gcp_services),
                                    columns=new_df.columns)
            new_df = pd.concat([new_df, empty_rows], ignore_index=True)

        # Update with GCP data
        new_df['AZURE Service'] = gcp_services['Service description'].values
        new_df['Consumption'] = gcp_services['Cost ($)'].values

        # Save to new Excel file
        output_filename = 'Updated-Azure-to-AWS-migration-with-GCP-data.xlsx'
        new_df.to_excel(output_filename, index=False)

        print(f"\nSuccessfully created {output_filename}")
        print(f"Updated file has {len(new_df)} rows")
        print("\nFirst few rows of updated file:")
        print(new_df[['AZURE Service', 'Consumption']].head(10))

        return output_filename

    except Exception as e:
        print(f"Error: {e}")
        return None

if __name__ == "__main__":
    output_file = extract_and_update_excel()
    if output_file:
        print(f"\n✅ Process completed successfully!")
        print(f"📁 Output file: {output_file}")
    else:
        print("❌ Process failed!")