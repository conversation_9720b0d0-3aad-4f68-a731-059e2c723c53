import pandas as pd
import os

def extract_and_update_excel():
    """
    Extract Service description and Cost from GCP CSV and create a copy of the Azure Excel file
    with updated Consumption Mapping sheet
    """
    try:
        # Read the GCP CSV file
        print("Reading GCP bill CSV file...")
        gcp_df = pd.read_csv('gcp_bill.csv')

        # Extract Service description and Cost columns
        gcp_services = gcp_df[['Service description', 'Cost ($)']].copy()

        # Sort by cost in descending order
        gcp_services = gcp_services.sort_values('Cost ($)', ascending=False)

        print(f"Extracted {len(gcp_services)} services from GCP bill")
        print("\nGCP Services (sorted by cost):")
        print(gcp_services)

        # Read all sheets from the original Excel file
        print("\nReading original Azure Excel file...")
        original_file = 'Azure-to-AWS-migration-scoping-with-Azure-Bills .xlsx'

        # Create output filename by adding "-output" before the extension
        base_name = os.path.splitext(original_file)[0]
        extension = os.path.splitext(original_file)[1]
        output_file = f"{base_name}-output{extension}"

        # Read all sheets into a dictionary
        all_sheets = pd.read_excel(original_file, sheet_name=None)

        print(f"Found sheets: {list(all_sheets.keys())}")

        # Get the Consumption Mapping sheet
        consumption_df = all_sheets['Consumption Mapping'].copy()
        print(f"Original Consumption Mapping sheet has {len(consumption_df)} rows")

        # Create updated dataframe with GCP data
        num_gcp_services = len(gcp_services)
        num_existing_rows = len(consumption_df)

        # Create new dataframe with GCP services
        updated_df = consumption_df.copy()

        # Clear existing data in AZURE Service and Consumption columns for all rows
        updated_df['AZURE Service'] = ''
        updated_df['Consumption'] = ''

        # If we have fewer GCP services than existing rows, only update the first N rows
        if num_gcp_services <= num_existing_rows:
            # Update only the first N rows with GCP data
            updated_df.loc[:num_gcp_services-1, 'AZURE Service'] = gcp_services['Service description'].values
            updated_df.loc[:num_gcp_services-1, 'Consumption'] = gcp_services['Cost ($)'].values
            # Keep the remaining rows but with empty AZURE Service and Consumption
        else:
            # If we have more GCP services, add more rows
            updated_df.loc[:num_existing_rows-1, 'AZURE Service'] = gcp_services['Service description'].iloc[:num_existing_rows].values
            updated_df.loc[:num_existing_rows-1, 'Consumption'] = gcp_services['Cost ($)'].iloc[:num_existing_rows].values

            # Add additional rows for remaining GCP services
            remaining_services = gcp_services.iloc[num_existing_rows:]
            for _, row in remaining_services.iterrows():
                new_row = pd.Series(index=updated_df.columns)
                new_row['AZURE Service'] = row['Service description']
                new_row['Consumption'] = row['Cost ($)']
                updated_df = pd.concat([updated_df, new_row.to_frame().T], ignore_index=True)

        # Update the sheet in the dictionary
        all_sheets['Consumption Mapping'] = updated_df

        # Write to the new output file (keeping original unchanged)
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            for sheet_name, sheet_df in all_sheets.items():
                sheet_df.to_excel(writer, sheet_name=sheet_name, index=False)

        print(f"\nSuccessfully created output file: {output_file}")
        print(f"Original file '{original_file}' remains unchanged")
        print(f"Consumption Mapping sheet in output file now has {len(updated_df)} rows")
        print("\nFirst few rows of updated Consumption Mapping sheet:")
        print(updated_df[['AZURE Service', 'Consumption']].head(10))

        return output_file

    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    updated_file = extract_and_update_excel()
    if updated_file:
        print(f"\n✅ Process completed successfully!")
        print(f"📁 Updated file: {updated_file}")
        print(f"🔄 The 'Consumption Mapping' sheet has been updated with GCP services and costs")
    else:
        print("❌ Process failed!")