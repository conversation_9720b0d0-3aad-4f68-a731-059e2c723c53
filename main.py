import pandas as pd
import os
from openpyxl import load_workbook
import shutil

def extract_and_update_excel():
    """
    Extract Service description and Cost from GCP CSV and create a copy of the Azure Excel file
    with updated Consumption Mapping sheet - preserving all formatting, colors, and formulas
    """
    try:
        # Read the GCP CSV file
        print("Reading GCP bill CSV file...")
        gcp_df = pd.read_csv('gcp_bill.csv')

        # Extract Service description and Cost columns
        gcp_services = gcp_df[['Service description', 'Cost ($)']].copy()

        # Sort by cost in descending order
        gcp_services = gcp_services.sort_values('Cost ($)', ascending=False)

        print(f"Extracted {len(gcp_services)} services from GCP bill")
        print("\nGCP Services (sorted by cost):")
        print(gcp_services)

        # Create output filename by adding "-output" before the extension
        original_file = 'Azure-to-AWS-migration-scoping-with-Azure-Bills (2).xlsx'
        base_name = os.path.splitext(original_file)[0]
        extension = os.path.splitext(original_file)[1]
        output_file = f"{base_name}-output{extension}"

        # Copy the original file to preserve all formatting, colors, and formulas
        print(f"\nCreating copy of original file...")
        shutil.copy2(original_file, output_file)

        # Load the copied workbook using openpyxl to preserve formatting
        print("Loading workbook with openpyxl to preserve formatting...")
        workbook = load_workbook(output_file)

        # Get the Consumption Mapping worksheet
        if 'Consumption Mapping' not in workbook.sheetnames:
            print("Error: 'Consumption Mapping' sheet not found!")
            return None

        worksheet = workbook['Consumption Mapping']

        print(f"Found 'Consumption Mapping' sheet with {worksheet.max_row} rows")

        # Find the column indices for 'AZURE Service' and 'Consumption'
        azure_service_col = None
        consumption_col = None

        # Check the first row for headers
        for col in range(1, worksheet.max_column + 1):
            cell_value = worksheet.cell(row=1, column=col).value
            if cell_value == 'AZURE Service':
                azure_service_col = col
            elif cell_value == 'Consumption':
                consumption_col = col

        if azure_service_col is None or consumption_col is None:
            print(f"Error: Could not find required columns. Found AZURE Service: {azure_service_col}, Consumption: {consumption_col}")
            return None

        print(f"Found columns - AZURE Service: {azure_service_col}, Consumption: {consumption_col}")

        # Clear existing data in these columns (starting from row 2 to preserve headers)
        print("Clearing existing data in AZURE Service and Consumption columns...")
        for row in range(2, worksheet.max_row + 1):
            worksheet.cell(row=row, column=azure_service_col).value = ""
            worksheet.cell(row=row, column=consumption_col).value = ""

        # Update with GCP data (starting from row 2)
        print("Updating with GCP data...")
        for idx, (_, service_row) in enumerate(gcp_services.iterrows()):
            row_num = idx + 2  # Start from row 2 (after header)
            worksheet.cell(row=row_num, column=azure_service_col).value = service_row['Service description']
            worksheet.cell(row=row_num, column=consumption_col).value = service_row['Cost ($)']

        # Save the workbook
        workbook.save(output_file)
        workbook.close()

        print(f"\nSuccessfully created output file: {output_file}")
        print(f"Original file '{original_file}' remains unchanged")
        print(f"Updated {len(gcp_services)} rows with GCP data")
        print("\nGCP Services added (sorted by cost):")
        for idx, (_, row) in enumerate(gcp_services.head(10).iterrows()):
            print(f"{idx+1}. {row['Service description']} - ${row['Cost ($)']}")

        return output_file

    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    updated_file = extract_and_update_excel()
    if updated_file:
        print(f"\n✅ Process completed successfully!")
        print(f"📁 Updated file: {updated_file}")
        print(f"🔄 The 'Consumption Mapping' sheet has been updated with GCP services and costs")
    else:
        print("❌ Process failed!")