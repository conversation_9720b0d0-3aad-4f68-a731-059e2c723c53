#!/usr/bin/env python3
"""
Simple script to run the Azure Resource Summary Generator
"""

import os
import sys
from azure_resource_summary_generator import AzureResourceSummaryGenerator

def main():
    """Run the summary generator with the Azure file"""
    
    # Default file path
    azure_file = "lbc/AzureResourceInventory_Report_2025-08-13_19_03.xlsx"
    
    # Check if file exists
    if not os.path.exists(azure_file):
        print(f"Error: File not found: {azure_file}")
        print("Please ensure the Azure Resource Inventory file is in the lbc/ directory")
        return
    
    print("Azure Resource Inventory Summary Generator")
    print("=" * 50)
    print(f"Processing file: {azure_file}")
    
    # Create generator and run
    generator = AzureResourceSummaryGenerator(azure_file)
    success = generator.create_summary_report()
    
    if success:
        output_file = azure_file.replace(".xlsx", "_Summary_Report.xlsx")
        print(f"\n✅ Summary report generated successfully!")
        print(f"📁 Output file: {output_file}")
    else:
        print("\n❌ Failed to generate summary report")

if __name__ == "__main__":
    main()
